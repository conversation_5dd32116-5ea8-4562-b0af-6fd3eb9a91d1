"use client"

import { useEffect, useState } from "react"

interface CountdownProps {
  // Optional targetDate for backward compatibility
  targetDate?: string
}

export function Countdown({ targetDate }: CountdownProps) {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
  })
  const [showNotice, setShowNotice] = useState(false)

  useEffect(() => {
    const getTimeUntilNextPacific8AM = () => {
      const now = new Date()

      // Get current Pacific time using Intl.DateTimeFormat
      const pacificFormatter = new Intl.DateTimeFormat('en-US', {
        timeZone: 'America/Los_Angeles',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })

      const pacificParts = pacificFormatter.formatToParts(now)
      const pacificYear = parseInt(pacificParts.find(p => p.type === 'year')?.value || '0')
      const pacificMonth = parseInt(pacificParts.find(p => p.type === 'month')?.value || '0')
      const pacificDay = parseInt(pacificParts.find(p => p.type === 'day')?.value || '0')
      const pacificHour = parseInt(pacificParts.find(p => p.type === 'hour')?.value || '0')

      // Create a date for 8 AM Pacific Time today
      const pacificDateStr = `${pacificYear}-${pacificMonth.toString().padStart(2, '0')}-${pacificDay.toString().padStart(2, '0')}`

      // Create target time by using a known Pacific timezone offset approach
      // We'll create the date and then convert it properly
      let targetPacific = new Date(`${pacificDateStr}T08:00:00`)

      // Get the timezone offset for Pacific Time at this specific date
      const tempDate = new Date(targetPacific.getTime())
      const utcTime = tempDate.getTime() + (tempDate.getTimezoneOffset() * 60000)
      const pacificTime = new Date(utcTime + (-8 * 3600000)) // Start with PST offset

      // Check if we're in daylight saving time by comparing with a known DST date
      const testDST = new Date(pacificYear, 6, 1) // July 1st
      const testSTD = new Date(pacificYear, 0, 1) // January 1st
      const isDST = testDST.getTimezoneOffset() < testSTD.getTimezoneOffset()

      // Adjust for daylight saving time
      if (isDST) {
        targetPacific = new Date(utcTime + (-7 * 3600000)) // PDT offset
      } else {
        targetPacific = new Date(utcTime + (-8 * 3600000)) // PST offset
      }

      // If we've already passed 8 AM Pacific today, set target to tomorrow
      if (pacificHour >= 8) {
        targetPacific.setDate(targetPacific.getDate() + 1)
      }

      const differenceMs = targetPacific.getTime() - now.getTime()

      if (differenceMs <= 0) {
        return { hours: 0, minutes: 0, seconds: 0 }
      }

      const hours = Math.floor(differenceMs / (1000 * 60 * 60))
      const minutes = Math.floor((differenceMs % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((differenceMs % (1000 * 60)) / 1000)

      return { hours, minutes, seconds }
    }

    const interval = setInterval(() => {
      const time = getTimeUntilNextPacific8AM()
      setTimeLeft(time)
    }, 1000)

    // Initial calculation
    const initialTime = getTimeUntilNextPacific8AM()
    setTimeLeft(initialTime)

    return () => clearInterval(interval)
  }, [])

  // Format the time with leading zeros
  const formattedHours = String(timeLeft.hours).padStart(2, "0")
  const formattedMinutes = String(timeLeft.minutes).padStart(2, "0")
  const formattedSeconds = String(timeLeft.seconds).padStart(2, "0")

  return showNotice ? (
    <div className="text-[14px] text-gray-400 cursor-pointer" onClick={() => setShowNotice(false)}>
      Updates daily at 8 AM PT
    </div>
  ) : (
    <div className="flex items-center gap-3 cursor-pointer" onClick={() => setShowNotice(true)}>
      <div className="text-[14px] text-gray-400 uppercase inline-flex items-center">refresh in</div>
      <div className="seven-segment text-[14px] text-gray-400 inline-flex items-center leading-none" style={{ transform: "translateY(1px)" }}>
        {formattedHours}:{formattedMinutes}:{formattedSeconds}
      </div>
    </div>
  )
}
